import React from 'react';
import { NAV_ITEMS } from '../../lib/constants/navigation';
import Logo from '../common/Logo';
import AuthButton from '../auth/AuthButton';
import { Link } from 'react-router-dom';

const FloatingNav = ({ isVisible = false }) => {
  if (!isVisible) {
    return null;
  }

  return (
    <div
      className="fixed top-4 left-0 right-0 w-full px-4 z-50"
      style={{ zIndex: 9999 }}
    >
      <div className="relative max-w-4xl mx-auto">
        <div
          className="
            relative
            rounded-full
            bg-gray-900/95
            border border-white/20
            shadow-lg
            flex
            items-center
            justify-between
            px-6
            py-3
          "
          style={{
            background: 'rgba(15, 23, 42, 0.95)',
            backdropFilter: 'blur(8px)',
            WebkitBackdropFilter: 'blur(8px)'
          }}
        >
          {/* Logo section */}
          <div className="flex-shrink-0 flex items-center">
            <Logo size="small" animated={false} />
          </div>

          {/* Navigation links */}
          <div className="hidden md:flex justify-center flex-1 mx-8">
            <div className="flex space-x-6 items-center">
              {NAV_ITEMS.map(({ name, href }) => (
                <Link
                  key={name}
                  to={href}
                  className="
                    relative
                    text-sm
                    text-white/80
                    hover:text-white
                    transition-colors
                    duration-200
                    group
                  "
                >
                  {name}
                  <span
                    className="
                      absolute
                      bottom-0
                      left-0
                      w-0
                      h-0.5
                      bg-blue-400
                      group-hover:w-full
                      transition-all
                      duration-200
                    "
                  />
                </Link>
              ))}
            </div>
          </div>

          {/* Auth button */}
          <div className="flex-shrink-0">
            <AuthButton compact={true} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default FloatingNav;